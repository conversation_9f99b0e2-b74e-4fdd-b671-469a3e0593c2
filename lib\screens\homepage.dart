import 'package:edu_writer_ai/app_theme/colors.dart';
import 'package:flutter/material.dart';
import '../widgets/app_drawer.dart';

class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: AppColors.background,
        appBar: PreferredSize(
          preferredSize: const Size.fromHeight(60),
          child: AppBar(
            backgroundColor: AppColors.primary,
            elevation: 0,
            iconTheme: const IconThemeData(color: Colors.transparent),
            title:
                const Text("Edu Writer", style: TextStyle(color: Colors.white)),
            leading: Builder(
              builder: (context) {
                return IconButton(
                  icon: const Icon(
                    Icons.menu,
                    color: AppColors.background,
                  ),
                  onPressed: () {
                    Scaffold.of(context).openDrawer();
                  },
                );
              },
            ),
          ),
        ),
        drawer: AppDrawer(),
        body: GridView(
    gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
      crossAxisCount: 2, // 2 columns
      crossAxisSpacing: 10,
      mainAxisSpacing: 10,
    ),
    children: List.generate(20, (index) {
      return Padding(
        padding: const EdgeInsets.all(8.0),
        child: Container(
          color: Colors.green.shade500,
          child: Center(
              child: Text('Item $index',
                  style: TextStyle(fontSize: 30, color: Colors.white))),
        ),
      );
    }),
  ),);
  }
}
